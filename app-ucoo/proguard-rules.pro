# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.kts.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keepattributes SourceFile
-keepattributes LineNumberTable
-keep class kotlin.coroutines.Continuation
-keep public class * extends java.lang.Exception
# 融云
-keepattributes Exceptions,InnerClasses

-keepattributes Signature

-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**

-ignorewarnings

# glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# kotlinx.serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt # core serialization annotations

# kotlinx-serialization-json specific. Add this if you have java.lang.NoClassDefFoundError kotlinx.serialization.json.JsonObjectSerializer
-keepclassmembers class kotlinx.serialization.json.** {
    *** Companion;
}
-keepclasseswithmembers class kotlinx.serialization.json.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# Change here com.qyqy.ucoo
-keep,includedescriptorclasses class com.qyqy.ucoo.**$$serializer { *; } # <-- change package name to your app's
-keepclassmembers class com.qyqy.ucoo.** { # <-- change package name to your app's
    *** Companion;
}
-keepclasseswithmembers class com.qyqy.ucoo.** { # <-- change package name to your app's
    kotlinx.serialization.KSerializer serializer(...);
}

# 阿里云上传
-keep class com.alibaba.sdk.android.oss.** { *; }
-dontwarn okio.**
-dontwarn org.apache.commons.codec.binary.**

# 声网rtc
 -keep class io.agora.**{*;}
 -dontwarn io.agora.**

 ##---------------Begin: proguard configuration for Gson  ----------
 # Gson uses generic type information stored in a class file when working with fields. Proguard
 # removes such information by default, so configure it to keep all of it.
 -keepattributes Signature

 # For using GSON @Expose annotation
 -keepattributes *Annotation*

 # Gson specific classes
 -dontwarn sun.misc.**
 #-keep class com.google.gson.stream.** { *; }

 # Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
 # JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
 -keep class * extends com.google.gson.TypeAdapter
 -keep class * implements com.google.gson.TypeAdapterFactory
 -keep class * implements com.google.gson.JsonSerializer
 -keep class * implements com.google.gson.JsonDeserializer

 # Prevent R8 from leaving Data object members always null
 -keepclassmembers,allowobfuscation class * {
   @com.google.gson.annotations.SerializedName <fields>;
 }

 # Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
 -keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
 -keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

 ##---------------End: proguard configuration for Gson  ----------
 #keep 类名和类成员都不会被移除和混淆
 -keep class * implements androidx.viewbinding.ViewBinding {
     *;
 }
-keep public class com.google.android.**{*;}
 # firebase
 -keep public class com.google.firebase.* {*;}
 # 坑
 -keep class androidx.constraintlayout.motion.widget.**{*;}
 # jsbridge
 -keep class com.smallbuer.jsbridge.core.** { *; }
 -keep class com.qyqy.ucoo.utils.web.** { *; }
 #
 -keep @androidx.annotation.Keep class * {*;}
 # 坑
 -keep class com.qyqy.ucoo.utils.ViewPager2SlowScrollMediator { *; }
 # 坑
 -keep class androidx.viewpager2.widget.** { *; }
 #CacheWebview
 -dontwarn ren.yale.android.cachewebviewlib.**
 -keep class ren.yale.android.cachewebviewlib.**{*;}
 -dontwarn retrofit2.**
 -keep class retrofit2.Call { *; }
 # 微信支付
 -keep class com.tencent.mm.opensdk.** { *;}
 -keep class com.tencent.mm.wxop.** { *;}
 -keep class com.tencent.mm.sdk.** { *;}

 -keep class com.qyqy.ucoo.core.rtm.RtmManager { *; }

#Adjust
-keep class com.adjust.sdk.**{ *; }
-keep class com.google.android.gms.common.ConnectionResult {
    int SUCCESS;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient {
    com.google.android.gms.ads.identifier.AdvertisingIdClient$Info getAdvertisingIdInfo(android.content.Context);
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
    java.lang.String getId();
    boolean isLimitAdTrackingEnabled();
}
-keep public class com.android.installreferrer.**{ *; }
-keep public class com.adjust.sdk.**{ *; }

# 即构
-keep class **.zego.**{*;}

 # 数美
 -keep class com.ishumei.** {*;}

 # 腾讯rtc
 -keep class com.tencent.** { *; }
 # 腾讯实时语音识别
 -keepclasseswithmembernames class * { # 保持 native 方法不被混淆
 native <methods>;
 }
 -keep public class com.tencent.aai.*
 -keep public class com.qq.wx.voice.*

 -keep class * implements com.qyqy.ucoo.compose.presentation.ComposeScreen{
    public java.lang.String key();
 }

 #不混淆内部类
 -keepattributes InnerClasses

 #不混淆jni调用类
 -keepclasseswithmembers class *{
     native <methods>;
 }

 ###################### faceverify-BEGIN ###########################
 -ignorewarnings
 -keep public class com.tencent.ytcommon.**{*;}
 -keep class com.tencent.turingface.sdk.*.TNative$aa { public *; }
 -keep class com.tencent.turingface.sdk.*.TNative$aa$bb { public *; }
 -keep class com.tencent.turingcam.** {*;}

 -keep public class com.tencent.youtu.ytagreflectlivecheck.jni.**{*;}
 -keep public class com.tencent.youtu.ytagreflectlivecheck.YTAGReflectLiveCheckInterface{
     public <methods>;
 }
 -keep public class com.tencent.youtu.ytposedetect.jni.**{*;}
 -keep public class com.tencent.youtu.ytposedetect.data.**{*;}
 -keep public class com.tencent.youtu.liveness.YTDeviceInfo{*;}
 -keep public class com.tencent.youtu.liveness.YTFaceTracker{*;}
 -keep public class com.tencent.youtu.liveness.YTFaceTracker$*{*;}
 -keep public class com.tencent.youtu.sdkkitframework.liveness.framework.YtSDKKitFrameworkTool{
    public *;
 }
 -keep public class com.tencent.youtu.sdkkitframework.liveness.common.YTImageData{
    *;
 }
 -keep public class com.tencent.cloud.huiyansdkface.facelight.net.*$*{
     *;
 }
 -keep public class com.tencent.cloud.huiyansdkface.facelight.net.**{
     *;
 }
 -keep public class com.tencent.cloud.huiyansdkface.facelight.provider.WbDeviceRiskInfo{
     public <fields>;
 }
 -keep public class com.tencent.cloud.huiyansdkface.facelight.provider.WbUiTips{
     *;
 }

 #================数据上报混淆规则 start===========================
 #实体类
 -keep class com.tencent.cloud.huiyansdkface.analytics.EventSender{
     *;
 }
 -keep class com.tencent.cloud.huiyansdkface.analytics.EventSender$*{
     *;
 }
 -keep class com.tencent.cloud.huiyansdkface.analytics.WBSAEvent{
      *;
 }
 -keep class com.tencent.cloud.huiyansdkface.analytics.WBSAParam{
      *;
 }
 #================数据上报混淆规则 end===========================

 #######################faceverify-END#############################

 ####################### normal混淆规则-BEGIN#############################
 #不混淆内部类
 -keepattributes InnerClasses
 -keepattributes *Annotation*
 -keepattributes Signature
 -keepattributes Exceptions

 -keep public class com.tencent.cloud.huiyansdkface.normal.net.*$*{
     *;
 }
 -keep public class com.tencent.cloud.huiyansdkface.normal.net.*{
     *;
 }
 #bugly
 -keep class com.tencent.bugly.idasc.**{
     *;
 }
 #wehttp混淆规则
 -dontwarn com.tencent.cloud.huiyansdkface.okio.**
 -keep class com.tencent.cloud.huiyansdkface.okio.**{
     *;
 }
 -dontwarn com.tencent.cloud.huiyansdkface.okhttp3.OkHttpClient$Builder
 #for R8
 -keep,allowobfuscation,allowshrinking class * implements com.tencent.cloud.huiyansdkface.wehttp2.WeReq$Callback
 -keep,allowobfuscation,allowshrinking interface com.tencent.cloud.huiyansdkface.wehttp2.WeReq$Callback

 ####################### normal混淆规则-END#############################

-keep class com.tencent.imsdk.** { *; }