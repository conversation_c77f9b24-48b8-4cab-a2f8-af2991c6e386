import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import org.jetbrains.kotlin.konan.properties.Properties
import org.jetbrains.kotlin.konan.properties.loadProperties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
    id("com.qyqy.plugin")
}

val keystorePropertiesPath: String = rootProject.file("keystore.properties").path
val keystoreProperties: Properties = loadProperties(keystorePropertiesPath)

android {
    namespace = "com.qyqy.ucoo.lite"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.ucoofun.liteandroid"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        //下一步是否versionCode由环境变量来指定?
        versionCode = (System.getenv()["VERSION_CODE"] ?: libs.versions.versionCode.get()).toInt()
        versionName = System.getenv()["BUILD_VERSION_NAME"] ?: libs.versions.versionName.get()

        buildConfigField("String", "CODE_BRANCH", "\"${execCommand("git branch --show-current")}\"")
        buildConfigField("String", "CODE_COMMIT", "\"${execCommand("git rev-parse --short HEAD")}\"")
        buildConfigField("String", "BUILD_TIME", "\"${getCurrentTime()}\"")
    }

    packaging {
        resources.excludes += "DebugProbesKt.bin"
    }

    signingConfigs {
        create("release") {
            storeFile = rootProject.file(keystoreProperties["storeFile"].toString())
            keyAlias = keystoreProperties["keyAlias"].toString()
            keyPassword = keystoreProperties["keyPassword"].toString()
            storePassword = keystoreProperties["storePassword"].toString()
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false
            configure<CrashlyticsExtension> {
                mappingFileUploadEnabled = true
            }
            manifestPlaceholders["firebase_analytics_enable"] = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
        debug {
            initWith(getByName("release"))
            versionNameSuffix = ".10086"
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
            configure<CrashlyticsExtension> {
                mappingFileUploadEnabled = false
            }
        }
        create("preRelease") {
            initWith(getByName("release"))
            versionNameSuffix = ".10086"
            configure<CrashlyticsExtension> {
                mappingFileUploadEnabled = false
            }
        }
    }

    flavorDimensions += listOf("channel", "pay")

    productFlavors {
        create("play") {
            dimension = "channel"
            ndk {
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
            packaging {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
        create("official") {
            dimension = "channel"
        }
        create("googlePay") {
            dimension = "pay"
        }
        create("commonPay") {
            dimension = "pay"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs = listOf("-Xcontext-receivers", "-Xstring-concat=inline")
    }

    buildFeatures {
        buildConfig = true
    }

    viewBinding {
        enable = true
    }
    setProperty(
        "archivesBaseName",
        "${project.name}-${defaultConfig.versionName}.${defaultConfig.versionCode}_${getCurrentTime()}"
    )
}

dependencies {
    implementation(fileTree(mapOf("dir" to "../Libraries/commonLibs", "include" to listOf("*.jar", "*.aar"))))
    api(project(":lib-full"))
    api(project(":lib-app"))
}