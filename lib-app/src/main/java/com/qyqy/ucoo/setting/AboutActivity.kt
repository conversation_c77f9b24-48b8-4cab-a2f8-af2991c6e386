package com.qyqy.ucoo.setting

import android.content.Intent
import android.content.res.ColorStateList
import android.net.Uri
import android.os.Bundle
import android.view.ViewGroup.MarginLayoutParams
import androidx.compose.foundation.text.ClickableText
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.ExperimentalTextApi
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.core.view.isVisible
import com.overseas.common.ext.click
import com.overseas.common.ext.gradientDrawable
import com.overseas.common.ext.viewBinding
import com.overseas.common.utils.dp
import com.overseas.common.utils.dpF
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.collectValue
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.config.AppConfig
import com.qyqy.ucoo.core.upgrade.UpgradeEntityImpl
import com.qyqy.ucoo.core.upgrade.UpgradeManager
import com.qyqy.ucoo.databinding.ActivityAboutBinding
import com.qyqy.ucoo.isPlayChannel
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.utils.web.addTitleByUrl
import com.qyqy.ucoo.utils.web.cacheEnableByUrl
import kotlinx.coroutines.flow.collectLatest

class AboutActivity : BaseActivity() {

    private val binding by viewBinding(ActivityAboutBinding::inflate)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        with(binding) {
            if (isLightTheme()) {
                setLightStatusBar()
                val lp = llContent.layoutParams as? MarginLayoutParams
                lp?.also {
                    it.marginStart = 16.dp
                    it.marginEnd = 16.dp
                    llContent.layoutParams = it
                }
                llContent.background = gradientDrawable {
                    cornerRadius = 12.dpF
                    color = ColorStateList.valueOf(android.graphics.Color.WHITE)
                }
                divider.isVisible = true
                toolbar.root.setBackgroundColor(android.graphics.Color.WHITE)
            }
            toolbar.title.setText(R.string.about)
            toolbar.btnEnd.isVisible = false
            toolbar.btnStart.click {
                onBackPressedDispatcher.onBackPressed()
            }
            version.text = BuildConfig.VERSION_NAME
            layoutVersion.click {
                toast(getString(R.string.thanks_for_use))
            }
            layoutNewVersion.click {
                val settings = SettingsRepository.sSettingsFlow.value.getOrNull()
                if (settings?.hasNewVersion == true) {
                    if (isPlayChannel) {
                        navigateToMarket(packageName, "com.android.vending")
                    } else {
                        UpgradeManager.showUpgradeDialog(this@AboutActivity, UpgradeEntityImpl.newUpgradeEntity(settings))
                    }
                }
            }
            ivLogo.setOnClickListener {
                DebugActivity.clickIntoDebug(this@AboutActivity)
            }
            buildAgreement()
        }

        SettingsRepository.sSettingsFlow.collectValue({
            it
        }) {
            collectLatest {
                binding.newVersion.text =
                    if (it.hasNewVersion) getString(R.string.has_new_version) else getString(R.string.no_new_version)
                binding.newVersionTip.isVisible = it.hasNewVersion
            }
        }
    }

    private fun navigateToMarket(appPkg: String, marketPkg: String) {
        runCatching {
            val uri = Uri.parse("market://details?id=$appPkg")
            val intent = Intent(Intent.ACTION_VIEW)
            intent.data = uri
            intent.setPackage(marketPkg)
            startActivity(intent)
        }
    }

    private fun buildAgreement() {
        binding.tvAgreeAgreement.setContent {
            val colorAcc = colorResource(id = R.color.colorAccent)
            val t1 = stringResource(id = R.string._用户服务协议_)
            val t2 = stringResource(id = R.string._隐私协议_)
            val tAnd = stringResource(id = R.string.和)

            val text = remember {
                buildAnnotatedString {
                    pushStringAnnotation("agreement", annotation = "url1")
                    withStyle(SpanStyle(colorAcc)) {
                        append(t1)
                    }
                    pop()
                    withStyle(SpanStyle(Color(0xFF86909C))) {
                        append(tAnd)
                    }
                    pushStringAnnotation("privacy", annotation = "url2")
                    withStyle(SpanStyle(colorAcc)) {
                        append(t2)
                    }
                    pop()
                }
            }
            ClickableText(text = text) { offset ->
                text.getStringAnnotations("agreement", offset, offset).firstOrNull()?.also {
                    startActivity(
                        JsBridgeWebActivity.newIntentFromLogin(
                            this@AboutActivity,
                            AppConfig.URL_AGREEMENT.addTitleByUrl(getString(R.string.用户服务协议)).cacheEnableByUrl(false)
                        )
                    )
                }
                text.getStringAnnotations("privacy", offset, offset).firstOrNull()?.also {
                    startActivity(
                        JsBridgeWebActivity.newIntentFromLogin(
                            this@AboutActivity,
                            AppConfig.URL_PRIVACY.addTitleByUrl(getString(R.string.隐私协议)).cacheEnableByUrl(false)
                        )
                    )
                }
            }
        }
    }


}